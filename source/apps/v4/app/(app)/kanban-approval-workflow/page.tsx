"use client"

/**
 * Expense Approval Workflow - Kanban Board Implementation
 *
 * Following Intent Layout Guidelines:
 * - Uses Kanban board component from @/components/ui/shadcn-io/kanban/
 * - Uses crud-drawers for Create, Read, Update expense requests
 * - Implements drag-and-drop workflow with typical expense approval stages
 * - Uses blue color system throughout
 * - Fully functional with status updates and expense card details
 */

import React, { useState } from "react"
import { DragEndEvent } from "@dnd-kit/core"
import {
  Plus,
  MoreHorizontal,
  Calendar,
  Filter,
  Users,
  Search,
  Pencil,
  Trash2,
  DollarSign,
  FileText,
  Building2,
  User,
  Clock,
  CheckCircle2,
  XCircle,
  Eye,
  AlertCircle
} from "lucide-react"

import {
  KanbanProvider,
  KanbanBoard,
  KanbanHeader,
  KanbanCards,
  KanbanCard,
} from "@/components/ui/shadcn-io/kanban/index"
import { Button } from "@/registry/new-york-v4/ui/button"
import { Input } from "@/registry/new-york-v4/ui/input"
import { Badge } from "@/registry/new-york-v4/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/registry/new-york-v4/ui/avatar"
import { CrudCreateDrawer, type DynamicFormConfig, type DynamicViewConfig } from "@/components/crud-drawers"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/registry/new-york-v4/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/registry/new-york-v4/ui/dropdown-menu"
import { ConfirmModal } from "@/components/modal"

// Define types for expense items and approval stages
interface ExpenseItem {
  id: string
  name: string
  column: string
  description?: string
  amount: number
  category: string
  submitter: {
    name: string
    email: string
    avatar?: string
    initials: string
    department: string
  }
  submittedDate: string
  expenseDate: string
  merchant: string
  receiptUrl?: string
  priority: "low" | "medium" | "high" | "urgent"
  notes?: string[]
}

interface ApprovalColumn {
  id: string
  name: string
  count?: number
}

// Approval workflow stages
const approvalStages: ApprovalColumn[] = [
  { id: "pending-review", name: "Pending Review", count: 0 },
  { id: "in-review", name: "In Review", count: 0 },
  { id: "approved", name: "Approved", count: 0 },
  { id: "rejected", name: "Rejected", count: 0 },
]

// Mock expense data
const initialExpenses: ExpenseItem[] = [
  {
    id: "1",
    name: "Conference Travel Expense",
    column: "pending-review",
    description: "Flight tickets and accommodation for Q1 Tech Conference in San Francisco",
    amount: 2450.00,
    category: "Travel",
    submitter: {
      name: "Sarah Johnson",
      email: "<EMAIL>",
      initials: "SJ",
      department: "Marketing",
    },
    submittedDate: "2024-01-22T09:00:00Z",
    expenseDate: "2024-01-20",
    merchant: "Delta Airlines & Hilton Hotels",
    receiptUrl: "/receipts/travel-001.pdf",
    priority: "high",
    notes: ["Conference pre-approved by department head", "Budget allocation confirmed"],
  },
  {
    id: "2",
    name: "Client Dinner Meeting",
    column: "pending-review",
    description: "Business dinner with potential enterprise client - XYZ Corporation",
    amount: 345.50,
    category: "Meals & Entertainment",
    submitter: {
      name: "Michael Chen",
      email: "<EMAIL>",
      initials: "MC",
      department: "Sales",
    },
    submittedDate: "2024-01-21T18:30:00Z",
    expenseDate: "2024-01-21",
    merchant: "The Capital Grille",
    receiptUrl: "/receipts/dinner-002.pdf",
    priority: "medium",
    notes: ["High-value client relationship", "Sales pipeline: $500K opportunity"],
  },
  {
    id: "3",
    name: "Software License Renewal",
    column: "pending-review",
    description: "Annual Adobe Creative Cloud license for design team (5 seats)",
    amount: 1299.99,
    category: "Software & Subscriptions",
    submitter: {
      name: "Emily Rodriguez",
      email: "<EMAIL>",
      initials: "ER",
      department: "Marketing",
    },
    submittedDate: "2024-01-20T14:15:00Z",
    expenseDate: "2024-01-20",
    merchant: "Adobe Systems Inc.",
    receiptUrl: "/receipts/adobe-003.pdf",
    priority: "urgent",
    notes: ["Required for Q1 campaign launch", "No alternative available"],
  },
  {
    id: "4",
    name: "Team Building Event",
    column: "in-review",
    description: "Quarterly team building activity and lunch for engineering team",
    amount: 875.00,
    category: "Team Activities",
    submitter: {
      name: "David Park",
      email: "<EMAIL>",
      initials: "DP",
      department: "Engineering",
    },
    submittedDate: "2024-01-19T10:30:00Z",
    expenseDate: "2024-01-25",
    merchant: "TeamBond Activities",
    receiptUrl: "/receipts/team-004.pdf",
    priority: "low",
    notes: ["Scheduled for Jan 25th", "15 team members attending"],
  },
  {
    id: "5",
    name: "Office Equipment",
    column: "in-review",
    description: "Standing desks and ergonomic chairs for new hires (3 sets)",
    amount: 1650.75,
    category: "Office Supplies",
    submitter: {
      name: "Lisa Thompson",
      email: "<EMAIL>",
      initials: "LT",
      department: "HR",
    },
    submittedDate: "2024-01-18T11:20:00Z",
    expenseDate: "2024-01-18",
    merchant: "Office Depot Business",
    receiptUrl: "/receipts/equipment-005.pdf",
    priority: "medium",
    notes: ["New hire onboarding week of Jan 29", "Ergonomic assessment completed"],
  },
  {
    id: "6",
    name: "Professional Training",
    column: "approved",
    description: "Advanced React and TypeScript workshop for development team",
    amount: 1200.00,
    category: "Training & Development",
    submitter: {
      name: "Robert Kim",
      email: "<EMAIL>",
      initials: "RK",
      department: "Engineering",
    },
    submittedDate: "2024-01-17T09:00:00Z",
    expenseDate: "2024-01-17",
    merchant: "Frontend Masters",
    receiptUrl: "/receipts/training-006.pdf",
    priority: "high",
    notes: ["Approved by CTO", "Part of Q1 skill development plan"],
  },
  {
    id: "7",
    name: "Marketing Conference Registration",
    column: "approved",
    description: "Registration fee for Marketing Summit 2024",
    amount: 599.00,
    category: "Conference & Events",
    submitter: {
      name: "Jennifer Lee",
      email: "<EMAIL>",
      initials: "JL",
      department: "Marketing",
    },
    submittedDate: "2024-01-16T14:00:00Z",
    expenseDate: "2024-01-15",
    merchant: "Marketing Summit Inc.",
    receiptUrl: "/receipts/conference-007.pdf",
    priority: "medium",
    notes: ["Early bird discount applied", "Networking opportunity"],
  },
  {
    id: "8",
    name: "Taxi Service",
    column: "rejected",
    description: "Daily commute taxi expenses for one week",
    amount: 425.00,
    category: "Transportation",
    submitter: {
      name: "Alex Martinez",
      email: "<EMAIL>",
      initials: "AM",
      department: "Sales",
    },
    submittedDate: "2024-01-15T08:00:00Z",
    expenseDate: "2024-01-14",
    merchant: "City Cab Services",
    priority: "low",
    notes: ["Rejected: Daily commute not covered by policy", "Alternative: public transportation reimbursement available"],
  },
]

// Category options
const expenseCategories = [
  "Travel",
  "Meals & Entertainment",
  "Software & Subscriptions",
  "Office Supplies",
  "Training & Development",
  "Conference & Events",
  "Transportation",
  "Team Activities",
  "Marketing",
  "Other"
]

// Department options
const departments = [
  "Marketing",
  "Sales",
  "Engineering",
  "HR",
  "Finance",
  "Operations",
  "Customer Success",
  "Product"
]

// Priority badge styling
const getPriorityBadge = (priority: "low" | "medium" | "high" | "urgent") => {
  switch (priority) {
    case "urgent":
      return (
        <Badge className="bg-red-700 text-white hover:bg-red-700 text-xs">
          Urgent
        </Badge>
      )
    case "high":
      return (
        <Badge className="bg-red-600 text-white hover:bg-red-600 text-xs">
          High
        </Badge>
      )
    case "medium":
      return (
        <Badge className="bg-amber-600 text-white hover:bg-amber-600 text-xs">
          Medium
        </Badge>
      )
    case "low":
      return (
        <Badge className="bg-slate-600 text-white hover:bg-slate-600 text-xs">
          Low
        </Badge>
      )
  }
}

// Column color styling
const getColumnColor = (columnId: string) => {
  switch (columnId) {
    case "pending-review":
      return "bg-blue-200 text-blue-900"
    case "in-review":
      return "bg-amber-200 text-amber-900"
    case "approved":
      return "bg-emerald-600 text-white"
    case "rejected":
      return "bg-red-600 text-white"
    default:
      return "bg-slate-200 text-slate-800"
  }
}

// Category badge color
const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    "Travel": "bg-blue-100 text-blue-700 hover:bg-blue-100",
    "Meals & Entertainment": "bg-purple-100 text-purple-700 hover:bg-purple-100",
    "Software & Subscriptions": "bg-indigo-100 text-indigo-700 hover:bg-indigo-100",
    "Office Supplies": "bg-cyan-100 text-cyan-700 hover:bg-cyan-100",
    "Training & Development": "bg-emerald-100 text-emerald-700 hover:bg-emerald-100",
    "Conference & Events": "bg-pink-100 text-pink-700 hover:bg-pink-100",
    "Transportation": "bg-teal-100 text-teal-700 hover:bg-teal-100",
    "Team Activities": "bg-violet-100 text-violet-700 hover:bg-violet-100",
    "Marketing": "bg-fuchsia-100 text-fuchsia-700 hover:bg-fuchsia-100",
  }
  return colors[category] || "bg-slate-100 text-slate-700 hover:bg-slate-100"
}

export default function KanbanApprovalWorkflowPage() {
  const [columns, setColumns] = useState<ApprovalColumn[]>(approvalStages)
  const [expenses, setExpenses] = useState<ExpenseItem[]>(initialExpenses)
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [selectedColumn, setSelectedColumn] = useState<string | null>(null)
  const [selectedExpense, setSelectedExpense] = useState<ExpenseItem | null>(null)
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [expenseToDelete, setExpenseToDelete] = useState<string | null>(null)

  // Filter expenses
  const filteredExpenses = expenses.filter((expense) => {
    const matchesSearch =
      expense.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      expense.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      expense.submitter.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      expense.merchant.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = categoryFilter === "all" || expense.category === categoryFilter
    const matchesDepartment = departmentFilter === "all" || expense.submitter.department === departmentFilter
    const matchesPriority = priorityFilter === "all" || expense.priority === priorityFilter

    return matchesSearch && matchesCategory && matchesDepartment && matchesPriority
  })

  // Update column counts
  const updatedColumns = columns.map(column => ({
    ...column,
    count: filteredExpenses.filter(expense => expense.column === column.id).length
  }))

  // Drag and drop handler
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (!over || active.id === over.id) {
      return
    }

    const activeExpense = expenses.find((expense) => expense.id === active.id)
    if (!activeExpense) return

    // Find the target column
    const targetColumn = columns.find(col => col.id === over.id)?.id ||
                        expenses.find(expense => expense.id === over.id)?.column

    if (!targetColumn) return

    // Update the expense's column
    setExpenses(prev => prev.map(expense =>
      expense.id === active.id
        ? { ...expense, column: targetColumn }
        : expense
    ))
  }

  // Get unique values for filters
  const uniqueDepartments = Array.from(
    new Set(expenses.map(expense => expense.submitter.department))
  )

  // Statistics
  const totalExpenses = filteredExpenses.length
  const totalAmount = filteredExpenses.reduce((sum, exp) => sum + exp.amount, 0)
  const pendingCount = filteredExpenses.filter(exp => exp.column === "pending-review").length
  const approvedCount = filteredExpenses.filter(exp => exp.column === "approved").length

  // Expense view configuration
  const expenseViewConfig: DynamicViewConfig = {
    title: "Expense Request Details",
    header: {
      title: {
        fields: ["name"],
      },
      badge: {
        field: "column",
        variants: {
          "pending-review": { variant: "outline", label: "Pending Review" },
          "in-review": { variant: "secondary", label: "In Review" },
          "approved": { variant: "default", label: "Approved" },
          "rejected": { variant: "destructive", label: "Rejected" },
        },
      },
    },
    sections: [
      {
        id: "submitter",
        title: "Submitted By",
        icon: User,
        fields: [
          {
            id: "submitterName",
            label: "Employee",
            renderer: "text",
          },
          {
            id: "submitterEmail",
            label: "Email",
            renderer: "email",
          },
          {
            id: "submitterDepartment",
            label: "Department",
            renderer: "badge",
            variant: "outline",
          },
        ],
      },
      {
        id: "expense-details",
        title: "Expense Information",
        icon: FileText,
        fields: [
          {
            id: "amount",
            label: "Amount",
            renderer: "custom",
            customRenderer: (value) => (
              <span className="text-lg font-semibold text-blue-600">
                ${value?.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            ),
          },
          {
            id: "category",
            label: "Category",
            renderer: "custom",
            customRenderer: (value) => (
              <Badge className={getCategoryColor(value)}>
                {value}
              </Badge>
            ),
          },
          {
            id: "merchant",
            label: "Merchant",
            renderer: "text",
          },
          {
            id: "description",
            label: "Description",
            renderer: "text",
          },
          {
            id: "expenseDate",
            label: "Expense Date",
            renderer: "date",
          },
          {
            id: "priority",
            label: "Priority",
            renderer: "custom",
            customRenderer: (value) => getPriorityBadge(value as any),
          },
        ],
      },
      {
        id: "submission",
        title: "Submission Details",
        icon: Clock,
        fields: [
          {
            id: "submittedDate",
            label: "Submitted On",
            renderer: "date",
          },
          {
            id: "receiptUrl",
            label: "Receipt",
            renderer: "url",
          },
        ],
      },
      {
        id: "notes",
        title: "Notes & Comments",
        icon: FileText,
        fields: [
          {
            id: "notes",
            label: "Notes",
            renderer: "custom",
            customRenderer: (value) => {
              if (!value || !Array.isArray(value) || value.length === 0) {
                return <span className="text-gray-500 text-sm">No notes available</span>
              }
              return (
                <ul className="space-y-2">
                  {value.map((note, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                      <span className="text-blue-500 mt-1">•</span>
                      <span>{note}</span>
                    </li>
                  ))}
                </ul>
              )
            },
          },
        ],
      },
    ],
  }

  // Expense form configuration
  const expenseFormConfig: DynamicFormConfig = {
    title: selectedExpense ? "Edit Expense Request" : "Submit New Expense",
    description: selectedExpense
      ? "Update expense request information"
      : "Create a new expense request for approval",
    submitLabel: selectedExpense ? "Update Request" : "Submit for Approval",
    sections: [
      {
        id: "basic",
        title: "Expense Details",
        fields: [
          {
            id: "name",
            label: "Expense Title",
            type: "text",
            placeholder: "Brief description of the expense",
            required: true,
          },
          {
            id: "description",
            label: "Detailed Description",
            type: "textarea",
            placeholder: "Provide detailed information about this expense",
            required: true,
          },
          {
            id: "amount",
            label: "Amount (USD)",
            type: "number",
            placeholder: "0.00",
            required: true,
            validation: {
              min: 0.01,
            },
          },
          {
            id: "category",
            label: "Category",
            type: "select",
            required: true,
            options: expenseCategories.map(cat => ({ label: cat, value: cat })),
          },
          {
            id: "merchant",
            label: "Merchant/Vendor",
            type: "text",
            placeholder: "Who was this paid to?",
            required: true,
          },
          {
            id: "expenseDate",
            label: "Expense Date",
            type: "date",
            required: true,
          },
          {
            id: "priority",
            label: "Priority",
            type: "select",
            required: true,
            defaultValue: "medium",
            options: [
              { label: "Low", value: "low" },
              { label: "Medium", value: "medium" },
              { label: "High", value: "high" },
              { label: "Urgent", value: "urgent" },
            ],
          },
        ],
      },
      {
        id: "submitter",
        title: "Submitter Information",
        fields: [
          {
            id: "submitterName",
            label: "Your Name",
            type: "text",
            placeholder: "Full name",
            required: true,
          },
          {
            id: "submitterEmail",
            label: "Your Email",
            type: "email",
            placeholder: "<EMAIL>",
            required: true,
          },
          {
            id: "submitterDepartment",
            label: "Department",
            type: "select",
            required: true,
            options: departments.map(dept => ({ label: dept, value: dept })),
          },
        ],
      },
      {
        id: "additional",
        title: "Additional Information",
        fields: [
          {
            id: "receiptUrl",
            label: "Receipt URL",
            type: "url",
            placeholder: "https://...",
            helpText: "Link to receipt or supporting documentation",
          },
          {
            id: "notesText",
            label: "Additional Notes",
            type: "textarea",
            placeholder: "Any additional context or justification for this expense",
          },
        ],
      },
    ],
  }

  const handleAddExpense = () => {
    setSelectedColumn(null)
    setSelectedExpense(null)
    setIsDrawerOpen(true)
  }

  const handleAddExpenseToColumn = (columnId: string) => {
    setSelectedColumn(columnId)
    setSelectedExpense(null)
    setIsDrawerOpen(true)
  }

  const handleCardClick = (expense: ExpenseItem) => {
    setSelectedExpense(expense)
    setIsDrawerOpen(true)
  }

  const handleSubmitExpense = (data: Record<string, any>) => {
    const newExpense: ExpenseItem = {
      id: String(Date.now()),
      name: data.name,
      column: selectedColumn || "pending-review",
      description: data.description,
      amount: Number(data.amount),
      category: data.category,
      submitter: {
        name: data.submitterName,
        email: data.submitterEmail,
        initials: data.submitterName
          .split(' ')
          .map((n: string) => n[0])
          .join('')
          .toUpperCase()
          .slice(0, 2),
        department: data.submitterDepartment,
      },
      submittedDate: new Date().toISOString(),
      expenseDate: data.expenseDate,
      merchant: data.merchant,
      receiptUrl: data.receiptUrl,
      priority: data.priority as "low" | "medium" | "high" | "urgent",
      notes: data.notesText ? [data.notesText] : [],
    }
    setExpenses(prev => [...prev, newExpense])
    setIsDrawerOpen(false)
  }

  const handleEditExpense = (data: Record<string, any>) => {
    if (!selectedExpense) return

    setExpenses(prev => prev.map(expense =>
      expense.id === selectedExpense.id
        ? {
            ...expense,
            name: data.name,
            description: data.description,
            amount: Number(data.amount),
            category: data.category,
            merchant: data.merchant,
            expenseDate: data.expenseDate,
            priority: data.priority as "low" | "medium" | "high" | "urgent",
            receiptUrl: data.receiptUrl,
            submitter: {
              ...expense.submitter,
              name: data.submitterName,
              email: data.submitterEmail,
              department: data.submitterDepartment,
              initials: data.submitterName
                .split(' ')
                .map((n: string) => n[0])
                .join('')
                .toUpperCase()
                .slice(0, 2),
            },
            notes: data.notesText
              ? [...(expense.notes || []), data.notesText]
              : expense.notes,
          }
        : expense
    ))
    setIsDrawerOpen(false)
    setSelectedExpense(null)
  }

  const handleDeleteExpense = (expenseId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setExpenseToDelete(expenseId)
    setDeleteConfirmOpen(true)
  }

  const confirmDeleteExpense = () => {
    if (expenseToDelete) {
      setExpenses(prev => prev.filter(expense => expense.id !== expenseToDelete))
      setExpenseToDelete(null)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Expense Approval Workflow</h1>
            <p className="text-sm text-gray-600 mt-1">
              {totalExpenses} requests • ${totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })} total • {pendingCount} pending • {approvedCount} approved
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button variant="outline" className="text-blue-600 border-blue-300 hover:bg-blue-50">
              <Calendar className="h-4 w-4 mr-2" />
              Timeline
            </Button>
            <Button variant="outline" className="text-blue-600 border-blue-300 hover:bg-blue-50">
              <Users className="h-4 w-4 mr-2" />
              Team
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white" onClick={handleAddExpense}>
              <Plus className="h-4 w-4 mr-2" />
              Submit Expense
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center gap-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search expenses..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Category Filter */}
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-44 focus:ring-blue-500 focus:border-blue-500">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {expenseCategories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Department Filter */}
          <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
            <SelectTrigger className="w-40 focus:ring-blue-500 focus:border-blue-500">
              <SelectValue placeholder="Department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {uniqueDepartments.map((dept) => (
                <SelectItem key={dept} value={dept}>
                  {dept}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Priority Filter */}
          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-32 focus:ring-blue-500 focus:border-blue-500">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              <SelectItem value="urgent">Urgent</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            More Filters
          </Button>
        </div>
      </div>

      {/* Kanban Board */}
      <div className="p-6">
        <div className="h-[calc(100vh-200px)] overflow-x-auto">
          <KanbanProvider
            columns={updatedColumns}
            data={filteredExpenses}
            onDataChange={setExpenses}
            onDragEnd={handleDragEnd}
            className="h-full min-w-max"
          >
            {(column) => (
              <KanbanBoard
                key={column.id}
                id={column.id}
                className="flex flex-col h-full w-80 min-w-[320px] overflow-hidden flex-shrink-0"
              >
                <KanbanHeader className={`flex items-center justify-between p-4 ${getColumnColor(column.id)} border-b`}>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-sm">{column.name}</span>
                    <Badge className="bg-white bg-opacity-20 text-current text-xs px-2 py-0.5">
                      {column.count}
                    </Badge>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-current hover:bg-white hover:bg-opacity-20">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem className="text-blue-600" onClick={() => handleAddExpenseToColumn(column.id)}>
                        Add Expense
                      </DropdownMenuItem>
                      <DropdownMenuItem>View Statistics</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>Export Column</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </KanbanHeader>

                <KanbanCards id={column.id} className="flex-1 gap-3 p-3 overflow-y-auto">
                  {(expense: ExpenseItem) => (
                    <KanbanCard
                      key={expense.id}
                      id={expense.id}
                      name={expense.name}
                      column={expense.column}
                      className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer"
                    >
                      <div className="space-y-3">
                        {/* Header with Actions */}
                        <div className="flex items-start justify-between gap-2">
                          <h3 className="font-medium text-sm text-gray-900 flex-1 leading-tight">
                            {expense.name}
                          </h3>
                          <div className="flex items-center gap-1">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleCardClick(expense)
                              }}
                              className="flex-shrink-0 p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                              aria-label="View expense"
                            >
                              <Eye className="h-3.5 w-3.5" />
                            </button>
                            <button
                              onClick={(e) => handleDeleteExpense(expense.id, e)}
                              className="flex-shrink-0 p-1 text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                              aria-label="Delete expense"
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </button>
                          </div>
                        </div>

                        {/* Amount - Prominent Display */}
                        <div className="bg-blue-50 border border-blue-100 rounded-md px-3 py-2">
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-blue-600 font-medium">Amount</span>
                            <span className="text-lg font-bold text-blue-700">
                              ${expense.amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                            </span>
                          </div>
                        </div>

                        {/* Description */}
                        {expense.description && (
                          <p className="text-xs text-gray-600 line-clamp-2">
                            {expense.description}
                          </p>
                        )}

                        {/* Merchant and Category */}
                        <div className="space-y-1.5">
                          <div className="flex items-center gap-1.5 text-xs text-gray-600">
                            <Building2 className="h-3 w-3 text-gray-400" />
                            <span className="truncate">{expense.merchant}</span>
                          </div>
                          <div>
                            <Badge className={`${getCategoryColor(expense.category)} text-xs px-2 py-0.5`}>
                              {expense.category}
                            </Badge>
                          </div>
                        </div>

                        {/* Priority and Date */}
                        <div className="flex items-center justify-between gap-2 pt-2 border-t border-gray-100">
                          {getPriorityBadge(expense.priority)}
                          <span className="text-xs text-gray-500 flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(expense.expenseDate).toLocaleDateString()}
                          </span>
                        </div>

                        {/* Submitter */}
                        <div className="flex items-center gap-2 pt-1 border-t border-gray-100">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={expense.submitter.avatar} alt={expense.submitter.name} />
                            <AvatarFallback className="bg-blue-100 text-blue-700 text-xs">
                              {expense.submitter.initials}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <p className="text-xs font-medium text-gray-900 truncate">
                              {expense.submitter.name}
                            </p>
                            <p className="text-xs text-gray-500 truncate">
                              {expense.submitter.department}
                            </p>
                          </div>
                        </div>
                      </div>
                    </KanbanCard>
                  )}
                </KanbanCards>
              </KanbanBoard>
            )}
          </KanbanProvider>
        </div>
      </div>

      {/* Add/Edit Expense Drawer */}
      <CrudCreateDrawer
        isOpen={isDrawerOpen}
        onClose={() => {
          setIsDrawerOpen(false)
          setSelectedExpense(null)
        }}
        dynamicConfig={expenseFormConfig}
        dynamicViewConfig={expenseViewConfig}
        dynamicData={selectedExpense ? {
          ...selectedExpense,
          submitterName: selectedExpense.submitter.name,
          submitterEmail: selectedExpense.submitter.email,
          submitterDepartment: selectedExpense.submitter.department,
        } : undefined}
        onDynamicSubmit={handleSubmitExpense}
        onDynamicEdit={handleEditExpense}
        forceEditMode={!selectedExpense}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={deleteConfirmOpen}
        onClose={() => {
          setDeleteConfirmOpen(false)
          setExpenseToDelete(null)
        }}
        onConfirm={confirmDeleteExpense}
        title="Delete Expense Request"
        description="Are you sure you want to delete this expense request? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  )
}
