# Kanban Approval Workflow - Expense Management

## Overview
A complete expense approval workflow application built using Kanban board concepts, following Intent Layout Guidelines and the repository's component architecture patterns.

## Location
`source/apps/v4/app/(app)/kanban-approval-workflow`

## Features

### 1. Kanban Board Workflow
- **Four approval stages:**
  - Pending Review (Blue)
  - In Review (Amber)
  - Approved (Green)
  - Rejected (Red)
- Fully functional drag-and-drop between stages
- Real-time column counts
- Color-coded stage indicators

### 2. Expense Card Details
Each expense card displays:
- **Expense Title** - Brief description
- **Amount** - Prominently displayed with currency formatting
- **Description** - Detailed expense information
- **Merchant/Vendor** - Who was paid
- **Category** - Color-coded badge (Travel, Meals, Software, etc.)
- **Priority** - Urgent, High, Medium, Low with color coding
- **Expense Date** - When the expense occurred
- **Submitter Info** - Avatar, name, department

### 3. Advanced Filtering
- **Search** - By expense name, description, submitter, or merchant
- **Category Filter** - 10 expense categories
- **Department Filter** - Dynamic based on data
- **Priority Filter** - Urgent, High, Medium, Low
- Real-time filter application

### 4. CRUD Operations via Drawer
Following Intent Layout Guidelines:
- **Create** - Submit new expense requests
- **Read** - Enhanced view mode with organized sections
- **Update** - Edit existing requests
- **Delete** - With confirmation modal

### 5. Comprehensive Form Fields
**Expense Details:**
- Expense Title (required)
- Detailed Description (required)
- Amount in USD (required, min validation)
- Category selection (required)
- Merchant/Vendor (required)
- Expense Date (required)
- Priority level (required)

**Submitter Information:**
- Name (required)
- Email (required)
- Department (required)

**Additional Information:**
- Receipt URL (optional)
- Additional notes (optional)

### 6. View Mode with Sections
Organized information display:
- **Submitted By** - Employee details
- **Expense Information** - Amount, category, merchant, description, date, priority
- **Submission Details** - Submitted date, receipt link
- **Notes & Comments** - List of notes and approval history

### 7. Statistics Dashboard
Header displays:
- Total number of requests
- Total amount (formatted currency)
- Pending count
- Approved count

### 8. Blue Color System
Consistent blue theme throughout:
- Primary actions: `bg-blue-600 hover:bg-blue-700`
- Borders: `border-blue-300`
- Hover states: `hover:bg-blue-50`
- Text accents: `text-blue-600`
- Amount displays: Blue backgrounds with blue text

### 9. Category Color Coding
- Travel - Blue
- Meals & Entertainment - Purple
- Software & Subscriptions - Indigo
- Office Supplies - Cyan
- Training & Development - Emerald
- Conference & Events - Pink
- Transportation - Teal
- Team Activities - Violet
- Marketing - Fuchsia

### 10. Priority Badges
- **Urgent** - Red 700
- **High** - Red 600
- **Medium** - Amber 600
- **Low** - Slate 600

## Mock Data
Includes 8 sample expense requests:
1. Conference Travel - $2,450.00 (Pending Review, High)
2. Client Dinner - $345.50 (Pending Review, Medium)
3. Software License - $1,299.99 (Pending Review, Urgent)
4. Team Building - $875.00 (In Review, Low)
5. Office Equipment - $1,650.75 (In Review, Medium)
6. Professional Training - $1,200.00 (Approved, High)
7. Conference Registration - $599.00 (Approved, Medium)
8. Taxi Service - $425.00 (Rejected, Low)

## Component Architecture
- Uses `@/components/ui/shadcn-io/kanban/` for board functionality
- Uses `@/components/crud-drawers` for form/view operations
- Uses `@/components/modal` for confirmations
- Follows shadcn/ui component patterns
- Implements proper TypeScript typing

## Technical Implementation

### Drag & Drop
- Built on `@dnd-kit/core`
- Updates expense status on drop
- Visual feedback during drag
- Accessibility announcements

### State Management
- React useState for local state
- Real-time filtering and searching
- Column count updates
- Form validation

### Form Validation
- Required field validation
- Email format validation
- Number range validation (min amount)
- URL format validation
- Custom error messages

### Responsive Design
- Horizontal scroll for kanban board
- Fixed header and filters
- Responsive card layouts
- Mobile-friendly interactions

## Usage

### Submit New Expense
1. Click "Submit Expense" button in header
2. Fill in expense details form
3. Provide submitter information
4. Add optional receipt URL and notes
5. Click "Submit for Approval"

### Review Expenses
1. Browse expense cards in kanban columns
2. Drag cards between stages to update status
3. Click eye icon to view details
4. Use filters to find specific expenses

### Edit Expense
1. Click on an expense card
2. In view mode, click "Edit" button
3. Update fields as needed
4. Click "Update Request"

### Delete Expense
1. Click trash icon on expense card
2. Confirm deletion in modal
3. Expense is removed from board

### Filter Expenses
1. Use search bar for text search
2. Select category from dropdown
3. Select department from dropdown
4. Select priority level
5. Filters apply in real-time

## Best Practices Followed
- ✅ Intent Layout Guidelines compliance
- ✅ Blue color system consistency
- ✅ Proper component reuse
- ✅ TypeScript type safety
- ✅ Accessibility features
- ✅ Form validation
- ✅ Error handling
- ✅ Confirmation modals for destructive actions
- ✅ Responsive design patterns
- ✅ Clean code structure
- ✅ Comprehensive documentation

## Future Enhancements
- Approval workflow automation
- Email notifications
- Receipt file upload
- Bulk operations
- Export functionality
- Analytics dashboard
- Approval history timeline
- Comment threads
- Approval routing rules
- Budget tracking integration
