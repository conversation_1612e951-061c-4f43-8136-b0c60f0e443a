# Quick Start Guide - Kanban Approval Workflow

## Access the Application

### Development Server
```bash
# Navigate to the application
cd /Users/<USER>/Desktop/core-ui

# Start the development server (if not already running)
npm run dev
# or
yarn dev
# or
pnpm dev

# Open browser to:
http://localhost:3000/kanban-approval-workflow
```

## First Look

When you open the application, you'll see:

1. **Header Bar**
   - Title: "Expense Approval Workflow"
   - Statistics: Total requests, total amount, pending, approved
   - Action buttons: Timeline, Team, Submit Expense

2. **Filter Bar**
   - Search input
   - Category dropdown
   - Department dropdown
   - Priority dropdown
   - More Filters button

3. **Kanban Board** (4 columns)
   - Pending Review (Blue) - 3 expenses
   - In Review (Amber) - 2 expenses
   - Approved (Green) - 2 expenses
   - Rejected (Red) - 1 expense

## Try These Actions

### 1. Submit a New Expense (5 minutes)

**Step-by-step:**
```
1. Click "Submit Expense" button (top right)
   → Drawer opens from right side

2. Fill in Expense Details:
   - Expense Title: "Office Chair"
   - Description: "Ergonomic office chair for back support"
   - Amount: 299.99
   - Category: Select "Office Supplies"
   - Merchant: "IKEA Business"
   - Expense Date: Pick today's date
   - Priority: Select "Medium"

3. Fill in Submitter Information:
   - Name: "Your Name"
   - Email: "<EMAIL>"
   - Department: Select "Operations"

4. Optional: Add receipt URL and notes

5. Click "Submit for Approval"
   → New card appears in "Pending Review" column
   → Drawer closes
```

### 2. View Expense Details (1 minute)

**Step-by-step:**
```
1. Click on any expense card
   → Drawer opens showing expense details

2. Review information organized in sections:
   - Submitted By
   - Expense Information (with prominent amount)
   - Submission Details
   - Notes & Comments

3. Click X or click outside to close
```

### 3. Move Expense Through Approval (30 seconds)

**Step-by-step:**
```
1. Drag an expense card from "Pending Review"
   → Card lifts with shadow effect

2. Drop it on "In Review" column
   → Card moves to new column
   → Column counts update automatically

3. Drag from "In Review" to "Approved"
   → Expense now marked as approved

4. Try moving back to "Pending Review"
   → Works both ways!
```

### 4. Edit an Expense (2 minutes)

**Step-by-step:**
```
1. Click on an expense card
   → Drawer opens in view mode

2. Click "Edit" button (top right in drawer)
   → Form becomes editable

3. Update any field (e.g., change amount to 350.00)

4. Click "Update Request"
   → Changes saved
   → Card updates on board
```

### 5. Use Filters (1 minute)

**Step-by-step:**
```
1. Type "Conference" in search box
   → Only matching expenses show

2. Clear search, select "Travel" category
   → Only travel expenses show

3. Select "Marketing" department
   → Only marketing department expenses show

4. Select "High" priority
   → Only high priority items show

5. Reset all filters to see everything
```

### 6. Delete an Expense (30 seconds)

**Step-by-step:**
```
1. Hover over an expense card
   → Eye and Trash icons appear

2. Click trash icon
   → Confirmation modal appears

3. Click "Delete"
   → Expense removed from board
   → Column count updates

4. Or click "Cancel" to abort
```

## Keyboard Shortcuts

```
Tab             → Navigate between elements
Enter/Space     → Activate focused button
Escape          → Close drawer/modal
Arrow Keys      → Navigate in dropdowns
```

## Understanding the Data

### Sample Data Included
The application comes with 8 pre-populated expense requests:

**Pending Review (3 items)**
- Conference Travel - $2,450.00 (High)
- Client Dinner - $345.50 (Medium)
- Software License - $1,299.99 (Urgent)

**In Review (2 items)**
- Team Building - $875.00 (Low)
- Office Equipment - $1,650.75 (Medium)

**Approved (2 items)**
- Professional Training - $1,200.00 (High)
- Conference Registration - $599.00 (Medium)

**Rejected (1 item)**
- Taxi Service - $425.00 (Low)

### Expense Categories Available
1. Travel
2. Meals & Entertainment
3. Software & Subscriptions
4. Office Supplies
5. Training & Development
6. Conference & Events
7. Transportation
8. Team Activities
9. Marketing
10. Other

### Departments Available
1. Marketing
2. Sales
3. Engineering
4. HR
5. Finance
6. Operations
7. Customer Success
8. Product

## Visual Guide

### Card Anatomy
```
┌─────────────────────────────────────────┐
│ Expense Title              [👁] [🗑]    │  ← Title + Actions
├─────────────────────────────────────────┤
│ ┌─────────────────────────────────────┐ │
│ │ Amount            $1,234.56         │ │  ← Prominent Amount
│ └─────────────────────────────────────┘ │
│                                         │
│ Description text truncated to 2 lines   │  ← Description
│ if longer than that...                  │
│                                         │
│ 🏢 Merchant Name                        │  ← Merchant
│ [Category Badge]                        │  ← Category
│                                         │
├─────────────────────────────────────────┤
│ [Priority] 📅 Jan 20, 2024             │  ← Priority + Date
├─────────────────────────────────────────┤
│ [SJ] Sarah Johnson                      │  ← Submitter
│      Marketing                          │     & Department
└─────────────────────────────────────────┘
```

### Column States
```
Pending Review    In Review      Approved       Rejected
(Blue)           (Amber)        (Green)        (Red)
     ↓               ↓              ↓             ↓
  [Card]         [Card]         [Card]        [Card]
  [Card]         [Card]         [Card]          ↑
  [Card]            ↑              ↑          (End state)
     ↓          (Being            ↑
  (New)        reviewed)      (Success)
```

## Common Use Cases

### Scenario 1: Employee Submits Expense
```
Employee → Submit Expense → Fill Form → Submit
         → Appears in "Pending Review"
```

### Scenario 2: Manager Reviews
```
Manager → Views expense details
        → Drags to "In Review"
        → Reviews supporting docs
        → Drags to "Approved" or "Rejected"
```

### Scenario 3: Finance Department
```
Finance → Filters by "Approved" status
        → Views all approved expenses
        → Processes payments
```

### Scenario 4: Audit Trail
```
Auditor → Views expense
        → Checks submission date
        → Reviews notes section
        → Verifies receipt URL
```

## Troubleshooting

### Drawer Won't Open
- Check console for errors
- Ensure click handler is working
- Verify state management

### Drag & Drop Not Working
- Ensure @dnd-kit is installed
- Check if sensors are configured
- Verify column IDs match

### Filters Not Working
- Clear browser cache
- Check filter state variables
- Verify filter logic in code

### Form Validation Errors
- Check required fields
- Verify email format
- Ensure amount is positive
- Check date format

## Next Steps

### Customize the Application
1. Edit expense categories
2. Add new approval stages
3. Modify form fields
4. Change color scheme
5. Add new filters

### Integrate with Backend
1. Replace mock data with API calls
2. Add authentication
3. Implement real-time updates
4. Add file upload for receipts
5. Set up email notifications

### Enhance Features
1. Add approval routing rules
2. Implement budget tracking
3. Add analytics dashboard
4. Create reporting tools
5. Add bulk operations

## Resources

### Documentation
- README.md - Feature overview
- ARCHITECTURE.md - Technical details
- This file - Quick start guide

### Code Location
```
source/apps/v4/app/(app)/kanban-approval-workflow/page.tsx
```

### Component Dependencies
```
@/components/ui/shadcn-io/kanban/    → Kanban board
@/components/crud-drawers             → Form/View drawer
@/components/modal                    → Confirm modal
@/registry/new-york-v4/ui/*          → UI components
```

## Support

If you encounter issues:
1. Check the console for errors
2. Review component documentation
3. Verify all imports are correct
4. Ensure dependencies are installed
5. Check TypeScript types

## Happy Testing! 🎉

The application is fully functional and ready to use. Try all the features and see how the workflow handles expense approvals efficiently!
