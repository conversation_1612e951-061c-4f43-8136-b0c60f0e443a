# Kanban Approval Workflow - Architecture Guide

## File Structure
```
source/apps/v4/app/(app)/kanban-approval-workflow/
├── page.tsx              # Main application component
├── README.md             # Feature documentation
└── ARCHITECTURE.md       # This file
```

## Component Dependencies

### Core Components Used
```typescript
// Kanban Components
@/components/ui/shadcn-io/kanban/
  ├── KanbanProvider      // DnD context provider
  ├── KanbanBoard         // Column container
  ├── KanbanHeader        // Column header
  ├── KanbanCards         // Cards container
  └── KanbanCard          // Individual card

// CRUD Drawer
@/components/crud-drawers
  ├── CrudCreateDrawer    // Main drawer component
  ├── DynamicFormConfig   // Form configuration type
  └── DynamicViewConfig   // View configuration type

// Modal
@/components/modal
  └── ConfirmModal        // Delete confirmation

// UI Components
@/registry/new-york-v4/ui/
  ├── button
  ├── input
  ├── badge
  ├── avatar
  ├── select
  └── dropdown-menu
```

## Data Flow

```
┌─────────────────────────────────────────────────────────────┐
│                     Application State                        │
├─────────────────────────────────────────────────────────────┤
│  expenses: ExpenseItem[]                                     │
│  columns: ApprovalColumn[]                                   │
│  filters: { search, category, department, priority }        │
│  drawer: { isOpen, selectedExpense, selectedColumn }        │
│  modal: { deleteConfirmOpen, expenseToDelete }              │
└─────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│                    Filter Pipeline                           │
├─────────────────────────────────────────────────────────────┤
│  1. Search filter (name, description, submitter, merchant)  │
│  2. Category filter                                          │
│  3. Department filter                                        │
│  4. Priority filter                                          │
│  → filteredExpenses                                          │
└─────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│                   Column Grouping                            │
├─────────────────────────────────────────────────────────────┤
│  Group by expense.column:                                    │
│  - pending-review                                            │
│  - in-review                                                 │
│  - approved                                                  │
│  - rejected                                                  │
│  Update column counts                                        │
└─────────────────────────────────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────────┐
│                    Render Kanban                             │
├─────────────────────────────────────────────────────────────┤
│  KanbanProvider (DnD context)                                │
│  └── For each column:                                        │
│      ├── KanbanBoard (droppable container)                   │
│      │   ├── KanbanHeader (column title + count)            │
│      │   └── KanbanCards                                     │
│      │       └── For each expense in column:                 │
│      │           └── KanbanCard (draggable)                  │
│      │               └── ExpenseCardContent                  │
└─────────────────────────────────────────────────────────────┘
```

## User Interactions

### 1. Drag & Drop Workflow
```
User drags card
    ↓
handleDragEnd(event)
    ↓
Find active expense
    ↓
Find target column
    ↓
Update expense.column
    ↓
Re-render with new state
```

### 2. Create Expense Workflow
```
Click "Submit Expense"
    ↓
setIsDrawerOpen(true)
setSelectedExpense(null)
    ↓
CrudCreateDrawer opens in create mode
    ↓
User fills form
    ↓
Validation
    ↓
handleSubmitExpense(data)
    ↓
Create new ExpenseItem
    ↓
Add to expenses array
    ↓
Close drawer
```

### 3. View/Edit Expense Workflow
```
Click expense card
    ↓
handleCardClick(expense)
    ↓
setSelectedExpense(expense)
setIsDrawerOpen(true)
    ↓
CrudCreateDrawer opens in view mode
    ↓
User clicks "Edit" button
    ↓
Switch to edit mode
    ↓
User updates fields
    ↓
Validation
    ↓
handleEditExpense(data)
    ↓
Update expense in array
    ↓
Close drawer
```

### 4. Delete Expense Workflow
```
Click trash icon
    ↓
handleDeleteExpense(id, event)
    ↓
setDeleteConfirmOpen(true)
setExpenseToDelete(id)
    ↓
ConfirmModal opens
    ↓
User confirms
    ↓
confirmDeleteExpense()
    ↓
Filter out expense from array
    ↓
Close modal
```

## Type Definitions

### ExpenseItem
```typescript
interface ExpenseItem {
  id: string                    // Unique identifier
  name: string                  // Expense title
  column: string                // Current approval stage
  description?: string          // Detailed description
  amount: number                // Expense amount (USD)
  category: string              // Expense category
  submitter: {                  // Employee who submitted
    name: string
    email: string
    avatar?: string
    initials: string
    department: string
  }
  submittedDate: string         // ISO date when submitted
  expenseDate: string           // Date of actual expense
  merchant: string              // Vendor/merchant name
  receiptUrl?: string           // Link to receipt
  priority: "low" | "medium" | "high" | "urgent"
  notes?: string[]              // Additional notes
}
```

### ApprovalColumn
```typescript
interface ApprovalColumn {
  id: string       // Column identifier
  name: string     // Display name
  count?: number   // Number of items in column
}
```

## Configuration Objects

### Dynamic Form Config Structure
```typescript
{
  title: string
  description: string
  submitLabel: string
  sections: [
    {
      id: string
      title: string
      fields: [
        {
          id: string              // Form field ID
          label: string           // Field label
          type: string            // Input type
          placeholder?: string    // Placeholder text
          required?: boolean      // Validation
          options?: Array         // For select fields
          validation?: Object     // Custom validation
        }
      ]
    }
  ]
}
```

### Dynamic View Config Structure
```typescript
{
  title: string
  header: {
    title: { fields: string[] }
    badge: {
      field: string
      variants: Record<string, { variant, label }>
    }
  }
  sections: [
    {
      id: string
      title: string
      icon: Component
      fields: [
        {
          id: string
          label: string
          renderer: string          // How to display
          customRenderer?: Function // Custom display logic
          variant?: string          // Badge variant
        }
      ]
    }
  ]
}
```

## Styling System

### Color Palette
```typescript
// Primary Blue Theme
Primary:     bg-blue-600, hover:bg-blue-700
Secondary:   bg-blue-50, border-blue-300
Text:        text-blue-600, text-blue-700

// Column Colors
Pending:     bg-blue-200, text-blue-900
In Review:   bg-amber-200, text-amber-900
Approved:    bg-emerald-600, text-white
Rejected:    bg-red-600, text-white

// Priority Colors
Urgent:      bg-red-700
High:        bg-red-600
Medium:      bg-amber-600
Low:         bg-slate-600

// Category Colors (10 unique colors)
Each category has unique bg-{color}-100 text-{color}-700
```

### Layout Classes
```typescript
// Container
"min-h-screen bg-gray-50"

// Header (sticky)
"bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-10"

// Kanban board area
"h-[calc(100vh-200px)] overflow-x-auto"

// Kanban column
"w-80 min-w-[320px]"

// Card
"bg-white border border-gray-200 shadow-sm hover:shadow-md"
```

## Performance Considerations

### Filtering
- Filters apply in real-time to `expenses` array
- Uses JavaScript `filter()` method
- Considers: search, category, department, priority
- Results cached in `filteredExpenses`

### Column Counts
- Dynamically calculated from filtered results
- Updated on every filter change
- Uses `map()` to create new array with counts

### Drag & Drop
- Uses `@dnd-kit/core` for performance
- Collision detection: `closestCenter`
- Sensors: Mouse, Touch, Keyboard
- Accessibility announcements included

## Accessibility Features

### Keyboard Navigation
- Tab through interactive elements
- Enter/Space to activate buttons
- Keyboard sensor for drag & drop
- Focus indicators on all controls

### Screen Readers
- Semantic HTML elements
- ARIA labels on icon buttons
- Drag & drop announcements
- Form field associations (label + input)

### Visual Accessibility
- High contrast colors
- Focus ring indicators
- Clear button states
- Icon + text combinations

## Extension Points

### Adding New Approval Stages
1. Add to `approvalStages` array
2. Add color mapping in `getColumnColor()`
3. Update type in ExpenseItem interface if needed

### Adding New Expense Categories
1. Add to `expenseCategories` array
2. Add color mapping in `getCategoryColor()`
3. Form automatically includes new option

### Custom Validation Rules
1. Update field `validation` in form config
2. Add custom logic in `validateForm()`
3. Return appropriate error messages

### Additional Filters
1. Add state variable for new filter
2. Add Select component in filter bar
3. Update `filteredExpenses` logic
4. Provide options from data

## Testing Considerations

### Unit Tests
- Filter logic functions
- Validation functions
- Data transformation functions
- Color mapping functions

### Integration Tests
- Drag & drop between columns
- Form submission flow
- Edit and update flow
- Delete confirmation flow

### E2E Tests
- Complete expense submission
- Search and filter combinations
- Status change via drag & drop
- View → Edit → Save workflow
