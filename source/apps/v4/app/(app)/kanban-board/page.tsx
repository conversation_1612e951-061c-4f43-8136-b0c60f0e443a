"use client"

import React, { useState } from "react"
import { DragEndEvent } from "@dnd-kit/core"
import { Plus, MoreHorizontal, Calendar, Filter, Users, Search, Pencil, Trash2 } from "lucide-react"
import {
  KanbanProvider,
  KanbanBoard,
  KanbanHeader,
  KanbanCards,
  KanbanCard,
} from "@/components/ui/shadcn-io/kanban/index"
import { Button } from "@/registry/new-york-v4/ui/button"
import { Input } from "@/registry/new-york-v4/ui/input"
import { Badge } from "@/registry/new-york-v4/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/registry/new-york-v4/ui/avatar"
import { CrudCreateDrawer, type DynamicFormConfig, type DynamicViewConfig } from "@/components/crud-drawers"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/registry/new-york-v4/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/registry/new-york-v4/ui/dropdown-menu"
import { ConfirmModal } from "@/components/modal"

// Define types for kanban items and columns
interface KanbanItem {
  id: string
  name: string
  column: string
  description?: string
  priority: "low" | "medium" | "high"
  assignee?: {
    name: string
    avatar?: string
    initials: string
  }
  dueDate?: string
  tags?: string[]
}

interface KanbanColumn {
  id: string
  name: string
  count?: number
}

// Mock data
const initialColumns: KanbanColumn[] = [
  { id: "backlog", name: "Backlog", count: 5 },
  { id: "todo", name: "To Do", count: 3 },
  { id: "in-progress", name: "In Progress", count: 4 },
  { id: "review", name: "Review", count: 2 },
  { id: "done", name: "Done", count: 8 },
]

const initialItems: KanbanItem[] = [
  {
    id: "1",
    name: "Design System Updates",
    column: "backlog",
    description: "Update the design system components and documentation",
    priority: "high",
    assignee: {
      name: "Sarah Johnson",
      initials: "SJ",
    },
    dueDate: "2024-02-15",
    tags: ["design", "documentation"],
  },
  {
    id: "2",
    name: "API Integration",
    column: "backlog",
    description: "Integrate payment processing API",
    priority: "medium",
    assignee: {
      name: "Mike Chen",
      initials: "MC",
    },
    dueDate: "2024-02-20",
    tags: ["backend", "api"],
  },
  {
    id: "3",
    name: "User Dashboard",
    column: "todo",
    description: "Build the main user dashboard interface",
    priority: "high",
    assignee: {
      name: "Emma Wilson",
      initials: "EW",
    },
    dueDate: "2024-02-12",
    tags: ["frontend", "ui"],
  },
  {
    id: "4",
    name: "Mobile Responsive",
    column: "todo",
    description: "Make the application mobile responsive",
    priority: "medium",
    assignee: {
      name: "David Kim",
      initials: "DK",
    },
    tags: ["mobile", "css"],
  },
  {
    id: "5",
    name: "Database Migration",
    column: "in-progress",
    description: "Migrate from MySQL to PostgreSQL",
    priority: "high",
    assignee: {
      name: "Alex Turner",
      initials: "AT",
    },
    dueDate: "2024-02-18",
    tags: ["database", "migration"],
  },
  {
    id: "6",
    name: "Unit Testing",
    column: "in-progress",
    description: "Add comprehensive unit tests",
    priority: "medium",
    assignee: {
      name: "Lisa Park",
      initials: "LP",
    },
    tags: ["testing", "quality"],
  },
  {
    id: "7",
    name: "Security Audit",
    column: "review",
    description: "Conduct security audit and fix vulnerabilities",
    priority: "high",
    assignee: {
      name: "Robert Davis",
      initials: "RD",
    },
    dueDate: "2024-02-10",
    tags: ["security", "audit"],
  },
  {
    id: "8",
    name: "Performance Optimization",
    column: "done",
    description: "Optimize application performance and loading times",
    priority: "medium",
    assignee: {
      name: "Jennifer Lee",
      initials: "JL",
    },
    tags: ["performance", "optimization"],
  },
]

const getTagColor = (tag: string) => {
  const tagColors: Record<string, string> = {
    // Design & UI
    design: "bg-pink-100 text-pink-700 hover:bg-pink-100",
    ui: "bg-purple-100 text-purple-700 hover:bg-purple-100",
    frontend: "bg-indigo-100 text-indigo-700 hover:bg-indigo-100",
    mobile: "bg-violet-100 text-violet-700 hover:bg-violet-100",
    css: "bg-fuchsia-100 text-fuchsia-700 hover:bg-fuchsia-100",

    // Backend & Data
    backend: "bg-blue-100 text-blue-700 hover:bg-blue-100",
    api: "bg-cyan-100 text-cyan-700 hover:bg-cyan-100",
    database: "bg-sky-100 text-sky-700 hover:bg-sky-100",
    migration: "bg-teal-100 text-teal-700 hover:bg-teal-100",

    // Quality & Testing
    testing: "bg-emerald-100 text-emerald-700 hover:bg-emerald-100",
    quality: "bg-green-100 text-green-700 hover:bg-green-100",
    security: "bg-red-100 text-red-700 hover:bg-red-100",
    audit: "bg-orange-100 text-orange-700 hover:bg-orange-100",

    // Performance & Docs
    performance: "bg-amber-100 text-amber-700 hover:bg-amber-100",
    optimization: "bg-yellow-100 text-yellow-700 hover:bg-yellow-100",
    documentation: "bg-slate-100 text-slate-700 hover:bg-slate-100",
  }

  return tagColors[tag.toLowerCase()] || "bg-gray-100 text-gray-700 hover:bg-gray-100"
}

const getPriorityBadge = (priority: "low" | "medium" | "high") => {
  switch (priority) {
    case "high":
      return (
        <Badge className="bg-red-600 text-white hover:bg-red-600 text-xs">
          High
        </Badge>
      )
    case "medium":
      return (
        <Badge className="bg-amber-600 text-white hover:bg-amber-600 text-xs">
          Medium
        </Badge>
      )
    case "low":
      return (
        <Badge className="bg-slate-600 text-white hover:bg-slate-600 text-xs">
          Low
        </Badge>
      )
  }
}

const getColumnColor = (columnId: string) => {
  switch (columnId) {
    case "backlog":
      return "bg-slate-200 text-slate-800"
    case "todo":
      return "bg-blue-200 text-blue-900"
    case "in-progress":
      return "bg-amber-200 text-amber-900"
    case "review":
      return "bg-purple-200 text-purple-900"
    case "done":
      return "bg-emerald-600 text-white"
    default:
      return "bg-slate-200 text-slate-800"
  }
}

export default function KanbanBoardPage() {
  const [columns, setColumns] = useState<KanbanColumn[]>(initialColumns)
  const [items, setItems] = useState<KanbanItem[]>(initialItems)
  const [searchQuery, setSearchQuery] = useState("")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [assigneeFilter, setAssigneeFilter] = useState("all")
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [selectedColumn, setSelectedColumn] = useState<string | null>(null)
  const [selectedTask, setSelectedTask] = useState<KanbanItem | null>(null)
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [taskToDelete, setTaskToDelete] = useState<string | null>(null)

  const filteredItems = items.filter((item) => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesPriority = priorityFilter === "all" || item.priority === priorityFilter
    const matchesAssignee = assigneeFilter === "all" ||
                           item.assignee?.name === assigneeFilter

    return matchesSearch && matchesPriority && matchesAssignee
  })

  // Update column counts based on filtered items
  const updatedColumns = columns.map(column => ({
    ...column,
    count: filteredItems.filter(item => item.column === column.id).length
  }))

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (!over || active.id === over.id) {
      return
    }

    const activeItem = items.find((item) => item.id === active.id)
    if (!activeItem) return

    // Find the target column
    const targetColumn = columns.find(col => col.id === over.id)?.id ||
                        items.find(item => item.id === over.id)?.column

    if (!targetColumn) return

    // Update the item's column
    setItems(prev => prev.map(item =>
      item.id === active.id
        ? { ...item, column: targetColumn }
        : item
    ))
  }

  const uniqueAssignees = Array.from(
    new Set(items.map(item => item.assignee?.name).filter(Boolean))
  )

  const totalTasks = filteredItems.length
  const completedTasks = filteredItems.filter(item => item.column === "done").length
  const inProgressTasks = filteredItems.filter(item => item.column === "in-progress").length

  // Task view configuration - enhanced view mode before edit
  const taskViewConfig: DynamicViewConfig = {
    title: "Task Details",
    header: {
      title: {
        fields: ["name"],
      },
      badge: {
        field: "priority",
        variants: {
          high: { variant: "destructive", label: "High Priority" },
          medium: { variant: "secondary", label: "Medium Priority" },
          low: { variant: "outline", label: "Low Priority" },
        },
      },
    },
    sections: [
      {
        id: "details",
        title: "Task Information",
        fields: [
          {
            id: "description",
            label: "Description",
            renderer: "text",
          },
          {
            id: "column",
            label: "Status",
            renderer: "custom",
            customRenderer: (value) => {
              const col = columns.find(c => c.id === value)
              return (
                <Badge className={getColumnColor(col?.id || value)}>
                  {col?.name || value}
                </Badge>
              )
            },
          },
          {
            id: "priority",
            label: "Priority",
            renderer: "custom",
            customRenderer: (value) => getPriorityBadge(value as "low" | "medium" | "high"),
          },
          {
            id: "dueDate",
            label: "Due Date",
            renderer: "date",
          },
        ],
      },
      {
        id: "assignment",
        title: "Assignment",
        fields: [
          {
            id: "assigneeName",
            label: "Assigned To",
            renderer: "text",
          },
        ],
      },
      {
        id: "tags",
        title: "Tags",
        fields: [
          {
            id: "tags",
            label: "Tags",
            renderer: "custom",
            customRenderer: (value) => {
              if (!value || !Array.isArray(value) || value.length === 0) return null
              return (
                <div className="flex flex-wrap gap-1">
                  {value.map((tag, index) => (
                    <Badge
                      key={index}
                      className={`${getTagColor(tag)} text-xs px-2 py-0.5`}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              )
            },
          },
        ],
      },
    ],
  }

  // Available tags with colors matching the card display (light bg, bold text)
  const availableTags = [
    // Design & UI
    { label: "Design", value: "design", style: { badgeColor: "#fbcfe8", iconColor: "#be185d" } }, // pink-100 bg, pink-700 text
    { label: "UI", value: "ui", style: { badgeColor: "#e9d5ff", iconColor: "#7e22ce" } }, // purple-100 bg, purple-700 text
    { label: "Frontend", value: "frontend", style: { badgeColor: "#c7d2fe", iconColor: "#4338ca" } }, // indigo-100 bg, indigo-700 text
    { label: "Mobile", value: "mobile", style: { badgeColor: "#ddd6fe", iconColor: "#6d28d9" } }, // violet-100 bg, violet-700 text
    { label: "CSS", value: "css", style: { badgeColor: "#f5d0fe", iconColor: "#a21caf" } }, // fuchsia-100 bg, fuchsia-700 text
    // Backend & Data
    { label: "Backend", value: "backend", style: { badgeColor: "#bfdbfe", iconColor: "#1d4ed8" } }, // blue-100 bg, blue-700 text
    { label: "API", value: "api", style: { badgeColor: "#a5f3fc", iconColor: "#0e7490" } }, // cyan-100 bg, cyan-700 text
    { label: "Database", value: "database", style: { badgeColor: "#bae6fd", iconColor: "#0369a1" } }, // sky-100 bg, sky-700 text
    { label: "Migration", value: "migration", style: { badgeColor: "#99f6e4", iconColor: "#0f766e" } }, // teal-100 bg, teal-700 text
    // Quality & Testing
    { label: "Testing", value: "testing", style: { badgeColor: "#a7f3d0", iconColor: "#047857" } }, // emerald-100 bg, emerald-700 text
    { label: "Quality", value: "quality", style: { badgeColor: "#bbf7d0", iconColor: "#15803d" } }, // green-100 bg, green-700 text
    { label: "Security", value: "security", style: { badgeColor: "#fecaca", iconColor: "#b91c1c" } }, // red-100 bg, red-700 text
    { label: "Audit", value: "audit", style: { badgeColor: "#fed7aa", iconColor: "#c2410c" } }, // orange-100 bg, orange-700 text
    // Performance & Docs
    { label: "Performance", value: "performance", style: { badgeColor: "#fde68a", iconColor: "#b45309" } }, // amber-100 bg, amber-700 text
    { label: "Optimization", value: "optimization", style: { badgeColor: "#fef08a", iconColor: "#a16207" } }, // yellow-100 bg, yellow-700 text
    { label: "Documentation", value: "documentation", style: { badgeColor: "#e2e8f0", iconColor: "#334155" } }, // slate-100 bg, slate-700 text
  ]

  // Task form configuration - dynamic based on context
  const taskFormConfig: DynamicFormConfig = {
    title: selectedTask ? "Edit Task" : "Add New Task",
    description: selectedTask
      ? "Update task information"
      : "Create a new task in your kanban board",
    submitLabel: selectedTask ? "Save Changes" : "Create Task",
    sections: [
      {
        id: "basic",
        title: "Task Details",
        fields: [
          {
            id: "name",
            label: "Task Name",
            type: "text",
            placeholder: "Enter task name",
            required: true,
          },
          {
            id: "description",
            label: "Description",
            type: "textarea",
            placeholder: "Enter task description",
          },
          {
            id: "priority",
            label: "Priority",
            type: "select",
            required: true,
            defaultValue: "medium",
            options: [
              { label: "High", value: "high" },
              { label: "Medium", value: "medium" },
              { label: "Low", value: "low" },
            ],
          },
          {
            id: "column",
            label: "Status",
            type: "select",
            required: true,
            defaultValue: selectedColumn || "backlog",
            options: columns.map(col => ({ label: col.name, value: col.id })),
          },
          {
            id: "dueDate",
            label: "Due Date",
            type: "date",
          },
          {
            id: "assigneeName",
            label: "Assignee Name",
            type: "text",
            placeholder: "Enter assignee name",
          },
          {
            id: "tags",
            label: "Tags",
            type: "multiselect",
            placeholder: "Select tags",
            options: availableTags,
          },
        ],
      },
    ],
  }

  const handleAddTask = () => {
    setSelectedColumn(null)
    setSelectedTask(null)
    setIsDrawerOpen(true)
  }

  const handleAddTaskToColumn = (columnId: string) => {
    setSelectedColumn(columnId)
    setSelectedTask(null)
    setIsDrawerOpen(true)
  }

  const handleCardClick = (task: KanbanItem) => {
    console.log('handleCardClick called', task)
    setSelectedTask(task)
    setIsDrawerOpen(true)
    console.log('isDrawerOpen set to true')
  }

  const handleSubmitTask = (data: Record<string, any>) => {
    const newTask: KanbanItem = {
      id: String(Date.now()),
      name: data.name,
      column: data.column || selectedColumn || "backlog",
      description: data.description,
      priority: data.priority as "low" | "medium" | "high",
      dueDate: data.dueDate,
      assignee: data.assigneeName ? {
        name: data.assigneeName,
        initials: data.assigneeName.split(' ').map((n: string) => n[0]).join('').toUpperCase(),
      } : undefined,
      tags: Array.isArray(data.tags) ? data.tags : (data.tags ? [data.tags] : []),
    }
    setItems(prev => [...prev, newTask])
    setIsDrawerOpen(false)
  }

  const handleEditTask = (data: Record<string, any>) => {
    if (!selectedTask) return

    setItems(prev => prev.map(item =>
      item.id === selectedTask.id
        ? {
            ...item,
            name: data.name,
            column: data.column,
            description: data.description,
            priority: data.priority as "low" | "medium" | "high",
            dueDate: data.dueDate,
            assignee: data.assigneeName ? {
              name: data.assigneeName,
              initials: data.assigneeName.split(' ').map((n: string) => n[0]).join('').toUpperCase(),
            } : undefined,
            tags: Array.isArray(data.tags) ? data.tags : (data.tags ? [data.tags] : item.tags || []),
          }
        : item
    ))
    setIsDrawerOpen(false)
    setSelectedTask(null)
  }

  const handleDeleteTask = (taskId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setTaskToDelete(taskId)
    setDeleteConfirmOpen(true)
  }

  const confirmDeleteTask = () => {
    if (taskToDelete) {
      setItems(prev => prev.filter(item => item.id !== taskToDelete))
      setTaskToDelete(null)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Project Board</h1>
            <p className="text-sm text-gray-600 mt-1">
              {totalTasks} total tasks • {completedTasks} completed • {inProgressTasks} in progress
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button variant="outline" className="text-blue-600 border-blue-300 hover:bg-blue-50">
              <Calendar className="h-4 w-4 mr-2" />
              Timeline
            </Button>
            <Button variant="outline" className="text-blue-600 border-blue-300 hover:bg-blue-50">
              <Users className="h-4 w-4 mr-2" />
              Team
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white" onClick={handleAddTask}>
              <Plus className="h-4 w-4 mr-2" />
              Add Task
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center gap-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Priority Filter */}
          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-32 focus:ring-blue-500 focus:border-blue-500">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>

          {/* Assignee Filter */}
          <Select value={assigneeFilter} onValueChange={setAssigneeFilter}>
            <SelectTrigger className="w-40 focus:ring-blue-500 focus:border-blue-500">
              <SelectValue placeholder="Assignee" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Assignees</SelectItem>
              {uniqueAssignees.map((assignee) => (
                <SelectItem key={assignee} value={assignee!}>
                  {assignee}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            More Filters
          </Button>
        </div>
      </div>

      {/* Kanban Board */}
      <div className="p-6">
        <div className="h-[calc(100vh-200px)] overflow-x-auto">
          <KanbanProvider
            columns={updatedColumns}
            data={filteredItems}
            onDataChange={setItems}
            onDragEnd={handleDragEnd}
            className="h-full min-w-max"
          >
            {(column) => (
              <KanbanBoard
                key={column.id}
                id={column.id}
                className="flex flex-col h-full w-80 min-w-[320px] overflow-hidden flex-shrink-0"
              >
                <KanbanHeader className={`flex items-center justify-between p-4 ${getColumnColor(column.id)} border-b`}>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-sm">{column.name}</span>
                    <Badge className="bg-white bg-opacity-20 text-current text-xs px-2 py-0.5">
                      {column.count}
                    </Badge>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-current hover:bg-white hover:bg-opacity-20">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem className="text-blue-600" onClick={() => handleAddTaskToColumn(column.id)}>Add Task</DropdownMenuItem>
                      <DropdownMenuItem>Edit Column</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600 focus:text-red-600 focus:bg-red-50">Delete Column</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </KanbanHeader>

                <KanbanCards id={column.id} className="flex-1 gap-3 p-3 overflow-y-auto">
                  {(item: KanbanItem) => (
                    <KanbanCard
                      key={item.id}
                      id={item.id}
                      name={item.name}
                      column={item.column}
                      className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer"
                    >
                      <div className="space-y-3">
                        {/* Task Title */}
                        <div className="flex items-start justify-between gap-2">
                          <h3 className="font-medium text-sm text-gray-900 flex-1 leading-tight">
                            {item.name}
                          </h3>
                          <div className="flex items-center gap-1" data-no-drag>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleCardClick(item)
                              }}
                              className="flex-shrink-0 p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                              aria-label="Edit task"
                            >
                              <Pencil className="h-3.5 w-3.5" />
                            </button>
                            <button
                              onClick={(e) => handleDeleteTask(item.id, e)}
                              className="flex-shrink-0 p-1 text-red-600 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                              aria-label="Delete task"
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </button>
                          </div>
                        </div>

                        {/* Description */}
                        {item.description && (
                          <p className="text-xs text-gray-600 line-clamp-2">
                            {item.description}
                          </p>
                        )}

                        {/* Tags */}
                        {item.tags && item.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {item.tags.slice(0, 2).map((tag, index) => (
                              <Badge
                                key={index}
                                className={`${getTagColor(tag)} text-xs px-2 py-0.5`}
                              >
                                {tag}
                              </Badge>
                            ))}
                            {item.tags.length > 2 && (
                              <Badge className="bg-slate-100 text-slate-600 hover:bg-slate-100 text-xs px-2 py-0.5">
                                +{item.tags.length - 2}
                              </Badge>
                            )}
                          </div>
                        )}

                        {/* Priority and Due Date */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getPriorityBadge(item.priority)}
                            {item.dueDate && (
                              <span className="text-xs text-gray-500">
                                Due {new Date(item.dueDate).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Assignee */}
                        {item.assignee && (
                          <div className="flex items-center gap-2 pt-1 border-t border-gray-100">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={item.assignee.avatar} alt={item.assignee.name} />
                              <AvatarFallback className="bg-blue-100 text-blue-700 text-xs">
                                {item.assignee.initials}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-xs text-gray-600 truncate">
                              {item.assignee.name}
                            </span>
                          </div>
                        )}
                      </div>
                    </KanbanCard>
                  )}
                </KanbanCards>
              </KanbanBoard>
            )}
          </KanbanProvider>
        </div>
      </div>

      {/* Add/Edit Task Drawer */}
      <CrudCreateDrawer
        isOpen={isDrawerOpen}
        onClose={() => {
          setIsDrawerOpen(false)
          setSelectedTask(null)
        }}
        dynamicConfig={taskFormConfig}
        dynamicViewConfig={taskViewConfig}
        dynamicData={selectedTask ? {
          ...selectedTask,
          assigneeName: selectedTask.assignee?.name || "",
          tags: selectedTask.tags || [],
        } : undefined}
        onDynamicSubmit={handleSubmitTask}
        onDynamicEdit={handleEditTask}
        forceEditMode={true}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={deleteConfirmOpen}
        onClose={() => {
          setDeleteConfirmOpen(false)
          setTaskToDelete(null)
        }}
        onConfirm={confirmDeleteTask}
        title="Delete Task"
        description="Are you sure you want to delete this task? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  )
}