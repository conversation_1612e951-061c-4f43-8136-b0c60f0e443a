```mermaid
erDiagram
    APP {
        string id PK
        string name
        string description
        string ownerId FK
        datetime created_at
        datetime updated_at
    }

    MATERIAL_NODE {
        string id PK
        string type "enum: EventStorming|ERD|IntentLayout|KnowledgeBase"
        string title
        text content
        string created_by FK
        datetime created_at
        datetime updated_at
        int version
    }

    EVENT_STORMING {
        string id PK
        string material_node_id FK
        text events "sequence / sticky notes / domains"
    }

    ERD_NODE {
        string id PK
        string material_node_id FK
        json schema "entity definitions & relationships"
    }

    INTENT_LAYOUT {
        string id PK
        string material_node_id FK
        json intents "intent -> UI layout mapping"
    }

    KNOWLEDGE_BASE {
        string id PK
        string material_node_id FK
        text index "searchable requirements, docs, rules"
    }

    ARTIFACT {
        string id PK
        string material_node_id FK
        string kind "diagram|schema|export|seed-data"
        string uri
        datetime generated_at
    }

    USER {
        string id PK
        string name
        string email
    }

    APP ||--o{ MATERIAL_NODE : "has"
    MATERIAL_NODE ||--|{ EVENT_STORMING : "is_type"
    MATERIAL_NODE ||--|{ ERD_NODE : "is_type"
    MATERIAL_NODE ||--|{ INTENT_LAYOUT : "is_type"
    MATERIAL_NODE ||--|{ KNOWLEDGE_BASE : "is_type"

    MATERIAL_NODE ||--o{ ARTIFACT : "generates"
    USER ||--o{ MATERIAL_NODE : "creates"
    USER ||--o{ APP : "owns"

    EVENT_STORMING ||--o{ ERD_NODE : "informs"
    ERD_NODE ||--o{ INTENT_LAYOUT : "drives"
    KNOWLEDGE_BASE ||--o{ EVENT_STORMING : "references"
    KNOWLEDGE_BASE ||--o{ ERD_NODE : "references"
    KNOWLEDGE_BASE ||--o{ INTENT_LAYOUT : "references"
```
