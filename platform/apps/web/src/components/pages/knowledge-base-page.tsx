"use client";

import { useState } from "react";

export function KnowledgeBasePage() {
  const [searchQuery, setSearchQuery] = useState("");

  const articles = [
    {
      id: 1,
      title: "Getting Started with Event Storming",
      excerpt: "Learn the basics of event storming methodology and how to run effective sessions.",
      category: "Event Storming",
      lastUpdated: "2 days ago",
      readTime: "5 min read"
    },
    {
      id: 2,
      title: "AI Chat Best Practices",
      excerpt: "Tips and tricks for having productive conversations with AI assistants.",
      category: "AI Tools",
      lastUpdated: "1 week ago",
      readTime: "3 min read"
    },
    {
      id: 3,
      title: "Dashboard Metrics Guide",
      excerpt: "Understanding key performance indicators and how to interpret dashboard data.",
      category: "Analytics",
      lastUpdated: "3 days ago",
      readTime: "7 min read"
    },
    {
      id: 4,
      title: "Collaboration Tools Overview",
      excerpt: "Overview of available collaboration features and how to use them effectively.",
      category: "Collaboration",
      lastUpdated: "5 days ago",
      readTime: "4 min read"
    }
  ];

  const categories = ["All", "Event Storming", "AI Tools", "Analytics", "Collaboration"];
  const [selectedCategory, setSelectedCategory] = useState("All");

  const filteredArticles = articles.filter(article =>
    (selectedCategory === "All" || article.category === selectedCategory) &&
    (searchQuery === "" || article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
     article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <div className="p-6 h-full overflow-y-auto">
      <div className="max-w-5xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Knowledge Base</h2>
          <p className="text-gray-600">Find guides, documentation, and best practices</p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
              />
            </div>
            <div className="flex gap-2 flex-wrap">
              {categories.map((category) => (
                <button
                  key={category}
                  type="button"
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? "bg-gray-800 text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Articles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredArticles.map((article) => (
            <div key={article.id} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-start justify-between mb-3">
                <span className="px-3 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full">
                  {article.category}
                </span>
                <span className="text-xs text-gray-500">{article.lastUpdated}</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{article.title}</h3>
              <p className="text-gray-600 text-sm mb-4">{article.excerpt}</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">{article.readTime}</span>
                <button
                  type="button"
                  className="text-sm text-gray-800 hover:text-gray-600 font-medium"
                >
                  Read more →
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredArticles.length === 0 && (
          <div className="bg-white rounded-lg p-12 shadow-sm border border-gray-200 text-center">
            <div className="text-4xl mb-4">📚</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No articles found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>
    </div>
  );
}