"use client";

import { DrawIoEmbed } from 'react-drawio';

export function EventStormingPage() {
  return (
    <div className="p-6 h-full overflow-y-auto">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Event Storming</h2>
              <p className="text-gray-600">Collaborative domain modeling sessions</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}