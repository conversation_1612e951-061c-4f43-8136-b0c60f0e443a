"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";
import { DashboardPage } from "./pages/dashboard-page";
import { KnowledgeBasePage } from "./pages/knowledge-base-page";
import { EventStormingPage } from "./pages/event-storming-page";
import { AIChatPage } from "./pages/ai-chat-page";
import { ProfilePage } from "./pages/profile-page";

interface WorkspaceItem {
  id: string;
  name: string;
  avatar?: string;
  isActive?: boolean;
}

interface ActionItem {
  id: string;
  name: string;
  type: "channel" | "dm" | "section";
  unreadCount?: number;
  isActive?: boolean;
  icon?: string;
}

interface MainLayoutProps {
  workspaces: WorkspaceItem[];
  actions: ActionItem[];
  onWorkspaceSelect?: (workspaceId: string) => void;
  onActionSelect?: (actionId: string) => void;
}

export function MainLayout({
  workspaces,
  actions,
  onWorkspaceSelect,
  onActionSelect,
}: MainLayoutProps) {
  const [selectedWorkspace, setSelectedWorkspace] = useState(workspaces[0]?.id);
  const [selectedAction, setSelectedAction] = useState(actions[0]?.id);
  const [showProfile, setShowProfile] = useState(false);

  const handleWorkspaceClick = (workspaceId: string) => {
    setSelectedWorkspace(workspaceId);
    onWorkspaceSelect?.(workspaceId);
  };

  const handleActionClick = (actionId: string) => {
    setSelectedAction(actionId);
    setShowProfile(false);
    onActionSelect?.(actionId);
  };

  const handleProfileClick = () => {
    setShowProfile(true);
  };

  const renderCurrentPage = () => {
    if (showProfile) {
      return <ProfilePage />;
    }

    const currentAction = actions.find(a => a.id === selectedAction);
    if (!currentAction) return null;

    switch (currentAction.name) {
      case 'dashboard':
        return <DashboardPage />;
      case 'knowledge-base':
        return <KnowledgeBasePage />;
      case 'event-storming':
        return <EventStormingPage />;
      case 'ai-chat':
        return <AIChatPage />;
      default:
        return null;
    }
  };

  const getActionIcon = (actionName: string) => {
    switch (actionName) {
      case 'dashboard':
        return '📊';
      case 'knowledge-base':
        return '📚';
      case 'event-storming':
        return '🌪️';
      case 'ai-chat':
        return '🤖';
      default:
        return '#';
    }
  };

  return (
    <div className="flex h-full bg-gray-50">
      {/* Workspace Sidebar */}
      <div className="w-12 bg-gray-800 flex flex-col items-center py-3 space-y-2">
        {workspaces.map((workspace) => (
          <button
            type="button"
            key={workspace.id}
            onClick={() => handleWorkspaceClick(workspace.id)}
            data-test-id={`workspace_${workspace.id}`}
            className={cn(
              "w-8 h-8 rounded-lg flex items-center justify-center text-white font-medium transition-all duration-200 hover:rounded-md",
              selectedWorkspace === workspace.id
                ? "bg-gray-600 rounded-md"
                : "bg-gray-700 hover:bg-gray-600"
            )}
          >
            {workspace.avatar ? (
              <img
                src={workspace.avatar}
                alt={workspace.name}
                className="w-full h-full rounded-lg object-cover"
              />
            ) : (
              <span className="text-sm">
                {workspace.name.charAt(0).toUpperCase()}
              </span>
            )}
          </button>
        ))}

        {/* Add Workspace Button */}
        <button
          type="button"
          data-test-id="add_workspace"
          className="w-8 h-8 rounded-lg border-2 border-dashed border-gray-500 text-gray-400 hover:border-gray-400 hover:text-gray-300 transition-colors duration-200 flex items-center justify-center"
        >
          <span className="text-lg">+</span>
        </button>
      </div>

      {/* Actions/Channels Sidebar */}
      <div className="w-16 bg-gray-700 text-white flex flex-col">
        {/* Actions List */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-2 space-y-2">
            {actions.map((action) => (
              <div key={action.id} className="relative">
                <button
                  type="button"
                  onClick={() => handleActionClick(action.id)}
                  data-test-id={`action_${action.id}`}
                  className={cn(
                    "w-12 h-12 rounded-lg text-2xl transition-colors duration-200 flex items-center justify-center relative",
                    selectedAction === action.id
                      ? "bg-gray-600 text-white"
                      : "text-gray-300 hover:bg-gray-600 hover:text-white"
                  )}
                  title={action.name}
                >
                  {getActionIcon(action.name)}
                  {action.unreadCount && action.unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {action.unreadCount > 9 ? "9+" : action.unreadCount}
                    </span>
                  )}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* User Profile Section */}
        <div className="p-2 border-t border-gray-600">
          <div className="flex justify-center">
            <button
              type="button"
              onClick={handleProfileClick}
              className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${
                showProfile
                  ? "bg-gray-600 text-white"
                  : "bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
              }`}
              title="Profile"
            >
              <span className="text-sm font-medium">👤</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col bg-white">
        {/* Content Body */}
        <div className="flex-1 overflow-hidden">
          {renderCurrentPage()}
        </div>
      </div>
    </div>
  );
}