"use client";

import { MainLayout } from "@/components/main-layout";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { trpc } from "@/utils/trpc";

const mockWorkspaces = [
  { id: "workspace-1", name: "Acme Corp", isActive: true },
  { id: "workspace-2", name: "Design Team" },
  { id: "workspace-3", name: "Dev Team" },
];

const mockActions = [
  { id: "general", name: "general", type: "channel" as const, unreadCount: 3 },
  { id: "random", name: "random", type: "channel" as const },
  { id: "development", name: "development", type: "channel" as const, unreadCount: 1 },
  { id: "design", name: "design", type: "channel" as const },
  { id: "john-doe", name: "<PERSON>", type: "dm" as const, unreadCount: 2 },
  { id: "jane-smith", name: "<PERSON>", type: "dm" as const },
  { id: "alerts", name: "Alerts & Notifications", type: "section" as const },
];

export default function Home() {
  const [selectedWorkspace, setSelectedWorkspace] = useState("workspace-1");
  const [selectedAction, setSelectedAction] = useState("general");
  const healthCheck = useQuery(trpc.healthCheck.queryOptions());

  const handleWorkspaceSelect = (workspaceId: string) => {
    setSelectedWorkspace(workspaceId);
    console.log("Selected workspace:", workspaceId);
  };

  const handleActionSelect = (actionId: string) => {
    setSelectedAction(actionId);
    console.log("Selected action:", actionId);
  };

  return (
    <div className="h-full">
      <MainLayout
        workspaces={mockWorkspaces.map(w => ({ ...w, isActive: w.id === selectedWorkspace }))}
        actions={mockActions}
        onWorkspaceSelect={handleWorkspaceSelect}
        onActionSelect={handleActionSelect}
      >
      </MainLayout>
    </div>
  );
}
