import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "../index.css";
import Providers from "@/components/providers";
import { MainLayout } from "@/components/main-layout";

const geistSans = Geist({
	variable: "--font-geist-sans",
	subsets: ["latin"],
});

const geistMono = Geist_Mono({
	variable: "--font-geist-mono",
	subsets: ["latin"],
});

export const metadata: Metadata = {
	title: "Platform app",
	description: "Platform app",
};

export default function RootLayout() {
	const mockWorkspaces = [
		{ id: "1", name: "Core UI", isActive: true },
		{ id: "2", name: "Platform" },
		{ id: "3", name: "Design" },
	];

	const mockActions = [
		{ id: "1", name: "dashboard", type: "channel" as const, isActive: true },
		{ id: "2", name: "knowledge-base", type: "channel" as const },
		{ id: "3", name: "event-storming", type: "channel" as const },
		{ id: "4", name: "ai-chat", type: "channel" as const, unreadCount: 2 },
	];

	return (
		<html lang="en" suppressHydrationWarning>
			<body
				className={`${geistSans.variable} ${geistMono.variable} antialiased`}
			>
				<Providers>
					<div className="h-svh">
						<MainLayout
							workspaces={mockWorkspaces}
							actions={mockActions}
						/>
					</div>
				</Providers>
			</body>
		</html>
	);
}
